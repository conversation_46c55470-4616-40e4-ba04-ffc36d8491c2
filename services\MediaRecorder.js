/**
 * 事件发射器基类，提供统一的事件监听和触发机制
 */
class EventEmitter {
  constructor() {
    this._events = {}
  }

  /**
   * 注册事件监听器
   * @param {string} event 事件名称
   * @param {function} listener 监听器函数
   */
  on(event, listener) {
    if (!this._events[event]) {
      this._events[event] = []
    }
    this._events[event].push(listener)
  }

  /**
   * 注册一次性事件监听器
   * @param {string} event 事件名称
   * @param {function} listener 监听器函数
   */
  once(event, listener) {
    const onceWrapper = (...args) => {
      listener(...args)
      this.off(event, onceWrapper)
    }
    this.on(event, onceWrapper)
  }

  /**
   * 移除事件监听器
   * @param {string} event 事件名称
   * @param {function} listener 监听器函数
   */
  off(event, listener) {
    if (!this._events[event]) return

    if (!listener) {
      delete this._events[event]
      return
    }

    const index = this._events[event].indexOf(listener)
    if (index > -1) {
      this._events[event].splice(index, 1)
    }
  }

  /**
   * 触发事件
   * @param {string} event 事件名称
   * @param {...any} args 事件参数
   */
  emit(event, ...args) {
    if (!this._events[event]) return

    this._events[event].forEach((listener) => {
      try {
        listener(...args)
      } catch (error) {
        console.error(`Event listener error for ${event}:`, error)
      }
    })
  }

  /**
   * 移除所有事件监听器
   */
  removeAllListeners() {
    this._events = {}
  }
}

/**
 * 录音管理器类，专门管理录音功能
 */
class AudioRecorder extends EventEmitter {
  constructor() {
    super()
    this.recorder = null
    this.isRecording = false
    this.isPaused = false
    this.startTime = 0
    this.pauseStartTime = 0
    this.totalPausedDuration = 0
    this.config = {}
    this.currentQuestionId = null
    this.durationTimer = null
    this.isForceStopping = false
  }

  /**
   * 初始化录音器
   * @param {object} config 配置参数
   */
  async init(config = {}) {
    this.config = {
      format: "mp3",
      sampleRate: 16000,
      duration: 60000,
      ...config,
    }

    this.recorder = wx.getRecorderManager()
    this._bindEvents()
  }

  /**
   * 绑定录音器事件
   * @private
   */
  _bindEvents() {
    if (!this.recorder) return

    this.recorder.onStart((res) => {
      console.log("录音启动成功", res)
      this.isRecording = true
      this.emit("audio:start", {
        type: "audio",
        event: "start",
        data: {
          questionId: this.currentQuestionId,
          timestamp: Date.now(),
        },
      })
    })

    this.recorder.onStop((res) => {
      console.log("录音停止", res)
      if (this.isForceStopping) {
        this.isForceStopping = false
        return
      }

      this.isRecording = false
      this.emit("audio:stop", {
        type: "audio",
        event: "stop",
        data: {
          questionId: this.currentQuestionId,
          timestamp: Date.now(),
          result: res,
        },
      })
    })

    this.recorder.onError((res) => {
      console.log("录音错误", res)
      this.isRecording = false
      this.emit("audio:error", {
        type: "audio",
        event: "error",
        data: {
          questionId: this.currentQuestionId,
          timestamp: Date.now(),
          error: res,
        },
      })
    })

    this.recorder.onPause(() => {
      console.log("录音暂停")
      this.isPaused = true
      this._pauseTimer()
      this.emit("audio:pause", {
        type: "audio",
        event: "pause",
        data: {
          questionId: this.currentQuestionId,
          timestamp: Date.now(),
        },
      })
    })

    this.recorder.onResume(() => {
      console.log("录音恢复")
      this.isPaused = false
      this._resumeTimer()
      this.emit("audio:resume", {
        type: "audio",
        event: "resume",
        data: {
          questionId: this.currentQuestionId,
          timestamp: Date.now(),
        },
      })
    })

    this.recorder.onInterruptionBegin(() => {
      console.log("录音被中断")
      this.recorder.pause()
    })

    this.recorder.onInterruptionEnd(() => {
      console.log("录音中断结束")
      this.recorder.resume()
    })
  }
  /**
   * 开始录音
   * @param {string} questionId 问题ID
   * @returns {Promise<void>}
   */
  async start(questionId) {
    if (this.isRecording) {
      throw new Error("录音已在进行中")
    }

    if (!this.recorder) {
      throw new Error("录音器未初始化")
    }

    this.currentQuestionId = questionId
    this.startTime = Date.now()
    this.totalPausedDuration = 0
    this.isForceStopping = false

    try {
      const audioConfig = { ...this.config }
      // 优化录制时间不够的问题
      audioConfig.duration += 100

      this.recorder.start(audioConfig)
      this._startTimer()
    } catch (error) {
      console.error("录音启动失败:", error)
      throw new Error("录音启动失败")
    }
  }

  /**
   * 停止录音
   * @returns {Promise<object>}
   */
  async stop() {
    return new Promise((resolve, reject) => {
      if (!this.isRecording) {
        resolve(null)
        return
      }

      this._clearTimer()

      // 监听停止事件
      const onStop = (eventData) => {
        this.off("audio:stop", onStop)
        resolve(eventData.data.result)
      }

      const onError = (eventData) => {
        this.off("audio:error", onError)
        reject(eventData.data.error)
      }

      this.once("audio:stop", onStop)
      this.once("audio:error", onError)

      this.recorder.stop()
    })
  }

  /**
   * 强制停止录音
   */
  forceStop() {
    this.isForceStopping = true
    this._clearTimer()

    if (this.isRecording && this.recorder) {
      this.recorder.stop()
    }

    this.isRecording = false
    this.isPaused = false
  }

  /**
   * 暂停录音
   */
  pause() {
    if (this.isRecording && !this.isPaused) {
      this.recorder.pause()
    }
  }

  /**
   * 恢复录音
   */
  resume() {
    if (this.isRecording && this.isPaused) {
      this.recorder.resume()
    }
  }

  /**
   * 检查录音权限
   * @returns {Promise<boolean>}
   */
  async checkPermission() {
    return this._checkSinglePermission("scope.record")
  }

  /**
   * 检查单个权限
   * @param {string} scope 权限范围
   * @returns {Promise<boolean>}
   * @private
   */
  async _checkSinglePermission(scope) {
    try {
      const status = await new Promise((resolve) => {
        wx.getSetting({
          success: (res) => resolve(res.authSetting[scope]),
          fail: () => resolve(undefined),
        })
      })

      if (status === true) return true
      if (status === false) return false

      await wx.authorize({ scope })
      return true
    } catch (error) {
      console.error(`权限申请失败: ${scope}`, error)
      return false
    }
  }

  /**
   * 启动计时器
   * @private
   */
  _startTimer() {
    this._clearTimer()

    this.durationTimer = setInterval(() => {
      const elapsed = Date.now() - this.startTime - this.totalPausedDuration

      this.emit("audio:timeUpdate", {
        type: "audio",
        event: "timeUpdate",
        data: {
          questionId: this.currentQuestionId,
          timestamp: Date.now(),
          elapsed: elapsed,
          isPaused: this.isPaused,
        },
      })
    }, 1000)
  }

  /**
   * 暂停计时器
   * @private
   */
  _pauseTimer() {
    if (!this.isPaused) {
      this.pauseStartTime = Date.now()
      this._clearTimer()
    }
  }

  /**
   * 恢复计时器
   * @private
   */
  _resumeTimer() {
    if (this.isPaused) {
      this.totalPausedDuration += Date.now() - this.pauseStartTime
      this._startTimer()
    }
  }

  /**
   * 清除计时器
   * @private
   */
  _clearTimer() {
    if (this.durationTimer) {
      clearInterval(this.durationTimer)
      this.durationTimer = null
    }
  }

  /**
   * 销毁录音器
   */
  destroy() {
    this.forceStop()

    if (this.recorder) {
      this.recorder.offStart()
      this.recorder.offStop()
      this.recorder.offError()
      this.recorder.offPause()
      this.recorder.offResume()
      this.recorder.offInterruptionBegin()
      this.recorder.offInterruptionEnd()
      this.recorder = null
    }

    this.removeAllListeners()
  }
}

/**
 * 录屏管理器类，专门管理录屏功能
 */
class VideoRecorder extends EventEmitter {
  constructor() {
    super()
    this.camera = null
    this.isRecording = false
    this.config = {}
    this.currentQuestionId = null
    this.isForceStopping = false
  }

  /**
   * 初始化录屏器
   * @param {object} config 配置参数
   */
  async init(config = {}) {
    this.config = {
      timeout: 60, // 秒
      ...config,
    }

    this.camera = wx.createCameraContext()
  }

  /**
   * 开始录屏
   * @param {string} questionId 问题ID
   * @returns {Promise<void>}
   */
  async start(questionId) {
    if (this.isRecording) {
      throw new Error("录屏已在进行中")
    }

    if (!this.camera) {
      throw new Error("录屏器未初始化")
    }

    this.currentQuestionId = questionId
    this.isForceStopping = false

    try {
      this.camera.startRecord({
        timeout: this.config.timeout,
        timeoutCallback: (res) => {
          console.log("录屏超时回调", res)
          this.isRecording = false

          if (res?.tempVideoPath) {
            this.emit("video:timeout", {
              type: "video",
              event: "timeout",
              data: {
                questionId: this.currentQuestionId,
                timestamp: Date.now(),
                result: res,
              },
            })
          } else {
            this.emit("video:error", {
              type: "video",
              event: "error",
              data: {
                questionId: this.currentQuestionId,
                timestamp: Date.now(),
                error: res,
              },
            })
          }
        },
      })

      this.isRecording = true
      this.emit("video:start", {
        type: "video",
        event: "start",
        data: {
          questionId: this.currentQuestionId,
          timestamp: Date.now(),
        },
      })
    } catch (error) {
      console.error("录屏启动失败:", error)
      throw new Error("录屏启动失败")
    }
  }

  /**
   * 停止录屏
   * @returns {Promise<object>}
   */
  async stop() {
    return new Promise((resolve, reject) => {
      if (!this.isRecording) {
        resolve(null)
        return
      }

      this.camera.stopRecord({
        success: (res) => {
          console.log("录屏停止成功", res)
          if (this.isForceStopping) {
            this.isForceStopping = false
            return
          }

          this.isRecording = false
          this.emit("video:stop", {
            type: "video",
            event: "stop",
            data: {
              questionId: this.currentQuestionId,
              timestamp: Date.now(),
              result: res,
            },
          })
          resolve(res)
        },
        fail: (error) => {
          console.log("录屏停止失败", error)
          this.isRecording = false
          this.emit("video:error", {
            type: "video",
            event: "error",
            data: {
              questionId: this.currentQuestionId,
              timestamp: Date.now(),
              error: error,
            },
          })
          reject(error)
        },
      })
    })
  }

  /**
   * 强制停止录屏
   */
  forceStop() {
    this.isForceStopping = true

    if (this.isRecording && this.camera) {
      this.camera.stopRecord()
    }

    this.isRecording = false
  }

  /**
   * 检查摄像头权限
   * @returns {Promise<boolean>}
   */
  async checkPermission() {
    return this._checkSinglePermission("scope.camera")
  }

  /**
   * 检查单个权限
   * @param {string} scope 权限范围
   * @returns {Promise<boolean>}
   * @private
   */
  async _checkSinglePermission(scope) {
    try {
      const status = await new Promise((resolve) => {
        wx.getSetting({
          success: (res) => resolve(res.authSetting[scope]),
          fail: () => resolve(undefined),
        })
      })

      if (status === true) return true
      if (status === false) return false

      await wx.authorize({ scope })
      return true
    } catch (error) {
      console.error(`权限申请失败: ${scope}`, error)
      return false
    }
  }

  /**
   * 销毁录屏器
   */
  destroy() {
    this.forceStop()

    if (this.camera) {
      this.camera = null
    }

    this.removeAllListeners()
  }
}

/**
 * 媒体录制管理器，协调录音和录屏功能，提供向后兼容的API
 */
class MediaRecorderManager extends EventEmitter {
  constructor() {
    super()
    this.audioRecorder = new AudioRecorder()
    this.videoRecorder = new VideoRecorder()
    this.enableAudio = true
    this.enableVideo = true
    this.currentQuestionId = null
    this.questionRecord = {}
    this.onUpload = null // 向后兼容的上传回调

    // 绑定子组件事件
    this._bindSubEvents()
  }

  /**
   * 绑定子组件事件
   * @private
   */
  _bindSubEvents() {
    // 转发录音事件
    this.audioRecorder.on("audio:start", (eventData) => {
      this.emit("audio:start", eventData)
      this.emit("start", eventData) // 向后兼容
    })

    this.audioRecorder.on("audio:stop", (eventData) => {
      this.setQuestionAudio(eventData.data.questionId, eventData.data.result)
      this.emit("audio:stop", eventData)
      this.emit("stop", eventData) // 向后兼容
    })

    this.audioRecorder.on("audio:pause", (eventData) => {
      this.emit("audio:pause", eventData)
      this.emit("pause", eventData) // 向后兼容
    })

    this.audioRecorder.on("audio:resume", (eventData) => {
      this.emit("audio:resume", eventData)
      this.emit("resume", eventData) // 向后兼容
    })

    this.audioRecorder.on("audio:error", (eventData) => {
      this.emit("audio:error", eventData)
      this.emit("error", eventData) // 向后兼容
    })

    this.audioRecorder.on("audio:timeUpdate", (eventData) => {
      this.emit("audio:timeUpdate", eventData)
      this.emit("timeUpdate", eventData) // 向后兼容
    })

    // 转发录屏事件
    this.videoRecorder.on("video:start", (eventData) => {
      this.emit("video:start", eventData)
      this.emit("start", eventData) // 向后兼容
    })

    this.videoRecorder.on("video:stop", (eventData) => {
      this.setQuestionVideo(eventData.data.questionId, eventData.data.result)
      this.emit("video:stop", eventData)
      this.emit("stop", eventData) // 向后兼容
    })

    this.videoRecorder.on("video:timeout", (eventData) => {
      this.setQuestionVideo(eventData.data.questionId, eventData.data.result)
      this.emit("video:timeout", eventData)
      this.emit("timeout", eventData) // 向后兼容
    })

    this.videoRecorder.on("video:error", (eventData) => {
      this.emit("video:error", eventData)
      this.emit("error", eventData) // 向后兼容
    })
  }

  /**
   * 初始化录制器配置（向后兼容）
   * @param {object} config 配置参数
   */
  async init(config) {
    // 解构配置参数并设置默认值
    const {
      enableAudio = true,
      enableVideo = true,
      audioConfig = {
        format: "mp3",
        sampleRate: 16000,
      },
      duration = 60000,
      autoHandlePermission = true,
      onTimeUpdate = null,
      onTimeout = null,
      onError = null,
      onPause = null,
      onResume = null,
    } = config

    if (!enableAudio && !enableVideo) {
      throw new Error("必须启用至少一个录制类型")
    }

    console.log("库 录音初始化", enableAudio)

    // 设置启用状态
    this.enableAudio = enableAudio
    this.enableVideo = enableVideo

    // 初始化子组件
    if (enableAudio) {
      await this.audioRecorder.init({
        ...audioConfig,
        duration: duration,
      })
    }

    if (enableVideo) {
      await this.videoRecorder.init({
        timeout: duration / 1000, // 转换为秒
      })
    }

    // 设置向后兼容的回调函数
    if (onTimeUpdate) {
      this.on("timeUpdate", (eventData) => {
        const ms = eventData.data.elapsed
        onTimeUpdate(ms, {
          question_id: eventData.data.questionId,
          isPaused: eventData.data.isPaused,
        })
      })
    }

    if (onTimeout) {
      this.on("timeout", () => {
        onTimeout()
      })
    }

    if (onError) {
      this.on("error", (eventData) => {
        const errorData = {}
        if (eventData.type === "audio") {
          errorData.audio = eventData.data.error
        } else if (eventData.type === "video") {
          errorData.video = eventData.data.error
        }
        onError(errorData)
      })
    }

    if (onPause) {
      this.on("pause", () => {
        onPause()
      })
    }

    if (onResume) {
      this.on("resume", () => {
        onResume()
      })
    }
  }

  /**
   * 设置问题音频数据
   * @param {string} questionId 问题ID
   * @param {object} value 音频数据
   */
  setQuestionAudio(questionId, value) {
    const question = this.questionRecord[questionId] || {}
    question.audio = value
    this.questionRecord[questionId] = question
  }

  /**
   * 获取问题音频数据
   * @param {string} questionId 问题ID
   * @returns {object} 音频数据
   */
  getQuestionAudio(questionId) {
    return this.questionRecord[questionId]?.audio
  }

  /**
   * 设置问题视频数据
   * @param {string} questionId 问题ID
   * @param {object} value 视频数据
   */
  setQuestionVideo(questionId, value) {
    const question = this.questionRecord[questionId] || {}
    question.video = value
    this.questionRecord[questionId] = question
  }

  /**
   * 获取问题视频数据
   * @param {string} questionId 问题ID
   * @returns {object} 视频数据
   */
  getQuestionVideo(questionId) {
    return this.questionRecord[questionId]?.video
  }

  /**
   * 开始录制（向后兼容）
   * @param {string} questionId 问题ID
   * @returns {Promise<void>}
   */
  async start(questionId) {
    this.currentQuestionId = questionId

    const promises = []

    if (this.enableAudio) {
      promises.push(this.audioRecorder.start(questionId))
    }

    if (this.enableVideo) {
      promises.push(this.videoRecorder.start(questionId))
    }

    try {
      await Promise.all(promises)
      console.log("库 启动成功")
    } catch (error) {
      console.log("库 启动异常", error)
      throw error
    }
  }
  /**
   * 停止录制（向后兼容）
   * @returns {Promise<object>} 包含录制结果的Promise
   */
  async stop() {
    console.log("库 触发封装 stop函数")

    const results = {
      audio: null,
      video: null,
      questionId: this.currentQuestionId,
      duration: 0,
    }

    const promises = []

    if (this.enableAudio) {
      promises.push(
        this.audioRecorder
          .stop()
          .then((audioResult) => {
            results.audio = audioResult
          })
          .catch((error) => {
            console.error("音频停止失败:", error)
          })
      )
    }

    if (this.enableVideo) {
      promises.push(
        this.videoRecorder
          .stop()
          .then((videoResult) => {
            results.video = videoResult
          })
          .catch((error) => {
            console.error("视频停止失败:", error)
          })
      )
    }

    try {
      await Promise.all(promises)
      return results
    } catch (error) {
      console.error("停止录制失败:", error)
      throw error
    }
  }

  /**
   * 强制停止录制（向后兼容）
   * @param {string} questionId 问题ID
   */
  forceStop(questionId) {
    if (this.enableAudio) {
      this.audioRecorder.forceStop()
    }

    if (this.enableVideo) {
      this.videoRecorder.forceStop()
    }

    // 清空当前问题数据
    if (questionId) {
      this.setQuestionAudio(questionId, null)
      this.setQuestionVideo(questionId, null)
    }
  }

  /**
   * 检查录音权限（向后兼容）
   * @returns {Promise<boolean>}
   */
  async checkRecordPermission() {
    if (this.enableAudio) {
      return await this.audioRecorder.checkPermission()
    }
    return true
  }

  /**
   * 检查摄像头权限（向后兼容）
   * @returns {Promise<boolean>}
   */
  async checkCameraPermission() {
    if (this.enableVideo) {
      return await this.videoRecorder.checkPermission()
    }
    return true
  }

  /**
   * 恢复录音（向后兼容）
   */
  resumeRecord() {
    if (this.enableAudio) {
      this.audioRecorder.resume()
    }
  }

  /**
   * 动态设置视频录制状态（向后兼容）
   * @param {boolean} enableVideo 是否启用视频
   */
  setEnableVideo(enableVideo) {
    this.enableVideo = enableVideo
  }

  /**
   * 打开系统设置页面进行权限管理（向后兼容）
   * @returns {Promise<void>}
   */
  async openPermissionSetting() {
    return new Promise((resolve) => {
      wx.openSetting({
        success: () => resolve(),
        fail: () => resolve(),
      })
    })
  }

  /**
   * 销毁录制器释放资源（向后兼容）
   */
  destroy() {
    console.log("触发销毁")

    if (this.audioRecorder) {
      this.audioRecorder.destroy()
    }

    if (this.videoRecorder) {
      this.videoRecorder.destroy()
    }

    this.removeAllListeners()
  }
}

// 为了向后兼容，导出 MediaRecorderManager 作为 MediaRecorder
const MediaRecorder = MediaRecorderManager

module.exports = MediaRecorder
