/**
 * MediaRecorder 新架构测试文件
 * 测试录音、录屏、事件监听等功能
 */

// 模拟微信小程序环境
global.wx = {
  getRecorderManager: () => {
    const callbacks = {}
    return {
      onStart: (cb) => {
        callbacks.onStart = cb
      },
      onStop: (cb) => {
        callbacks.onStop = cb
      },
      onError: (cb) => {
        callbacks.onError = cb
      },
      onPause: (cb) => {
        callbacks.onPause = cb
      },
      onResume: (cb) => {
        callbacks.onResume = cb
      },
      onInterruptionBegin: (cb) => {
        callbacks.onInterruptionBegin = cb
      },
      onInterruptionEnd: (cb) => {
        callbacks.onInterruptionEnd = cb
      },
      start: () => {
        setTimeout(() => callbacks.onStart && callbacks.onStart({}), 10)
      },
      stop: () => {
        setTimeout(
          () =>
            callbacks.onStop &&
            callbacks.onStop({ tempFilePath: "mock-audio.mp3" }),
          10
        )
      },
      pause: () => {
        setTimeout(() => callbacks.onPause && callbacks.onPause(), 10)
      },
      resume: () => {
        setTimeout(() => callbacks.onResume && callbacks.onResume(), 10)
      },
      offStart: () => {},
      offStop: () => {},
      offError: () => {},
      offPause: () => {},
      offResume: () => {},
      offInterruptionBegin: () => {},
      offInterruptionEnd: () => {},
    }
  },
  createCameraContext: () => ({
    startRecord: ({ timeoutCallback }) => {
      setTimeout(
        () =>
          timeoutCallback &&
          timeoutCallback({ tempVideoPath: "mock-video.mp4" }),
        50
      )
    },
    stopRecord: ({ success }) => {
      setTimeout(
        () => success && success({ tempVideoPath: "mock-video.mp4" }),
        10
      )
    },
  }),
  getSetting: ({ success }) => {
    success({ authSetting: { "scope.record": true, "scope.camera": true } })
  },
  authorize: () => Promise.resolve(),
  openSetting: ({ success }) => success(),
}

const MediaRecorder = require("./services/MediaRecorder")

async function testBasicFunctionality() {
  console.log("=== 测试基础功能 ===")

  const recorder = new MediaRecorder()

  // 测试初始化
  try {
    await recorder.init({
      enableAudio: true,
      enableVideo: true,
      duration: 10000,
      onTimeUpdate: (ms, data) => {
        console.log(`时间更新: ${ms}ms, 问题ID: ${data.question_id}`)
      },
      onTimeout: () => {
        console.log("录制超时")
      },
      onError: (err) => {
        console.log("录制错误:", err)
      },
      onPause: () => {
        console.log("录制暂停")
      },
      onResume: () => {
        console.log("录制恢复")
      },
    })
    console.log("✓ 初始化成功")
  } catch (error) {
    console.log("✗ 初始化失败:", error.message)
  }

  // 测试权限检查
  try {
    const audioPermission = await recorder.checkRecordPermission()
    const videoPermission = await recorder.checkCameraPermission()
    console.log(
      `✓ 权限检查 - 录音: ${audioPermission}, 录屏: ${videoPermission}`
    )
  } catch (error) {
    console.log("✗ 权限检查失败:", error.message)
  }

  return recorder
}

async function testEventSystem() {
  console.log("\n=== 测试事件系统 ===")

  const recorder = new MediaRecorder()
  await recorder.init({ enableAudio: true, enableVideo: false })

  // 测试细粒度事件监听
  recorder.on("audio:start", (eventData) => {
    console.log("✓ 收到 audio:start 事件:", eventData.data.questionId)
  })

  recorder.on("audio:stop", (eventData) => {
    console.log("✓ 收到 audio:stop 事件:", eventData.data.questionId)
  })

  recorder.on("audio:error", (eventData) => {
    console.log("✓ 收到 audio:error 事件:", eventData.data.error)
  })

  // 测试向后兼容事件
  recorder.on("start", (eventData) => {
    console.log("✓ 收到向后兼容 start 事件")
  })

  recorder.on("stop", (eventData) => {
    console.log("✓ 收到向后兼容 stop 事件")
  })

  console.log("✓ 事件监听器注册成功")

  return recorder
}

async function testRecordingFlow() {
  console.log("\n=== 测试录制流程 ===")

  const recorder = new MediaRecorder()
  await recorder.init({
    enableAudio: true,
    enableVideo: true,
    duration: 5000,
  })

  try {
    // 测试开始录制
    await recorder.start("test-question-1")
    console.log("✓ 录制开始成功")

    // 模拟录制一段时间
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 测试停止录制
    const result = await recorder.stop()
    console.log("✓ 录制停止成功:", result)
  } catch (error) {
    console.log("✗ 录制流程失败:", error.message)
  }

  return recorder
}

async function testErrorHandling() {
  console.log("\n=== 测试错误处理 ===")

  const recorder = new MediaRecorder()

  try {
    // 测试未初始化就开始录制
    await recorder.start("test-question-error")
    console.log("✗ 应该抛出错误但没有")
  } catch (error) {
    console.log("✓ 正确捕获错误:", error.message)
  }

  // 测试重复开始录制
  await recorder.init({ enableAudio: true, enableVideo: false })

  try {
    await recorder.start("test-question-1")
    await recorder.start("test-question-2") // 应该失败
    console.log("✗ 应该抛出重复录制错误但没有")
  } catch (error) {
    console.log("✓ 正确捕获重复录制错误:", error.message)
  }

  return recorder
}

async function testBackwardCompatibility() {
  console.log("\n=== 测试向后兼容性 ===")

  const recorder = new MediaRecorder()

  // 测试旧的API调用方式
  try {
    await recorder.init({
      enableAudio: true,
      enableVideo: true,
      duration: 10000,
      onTimeUpdate: (ms, data) => {
        console.log("✓ 向后兼容的 onTimeUpdate 回调工作正常")
      },
      onError: (err) => {
        console.log("✓ 向后兼容的 onError 回调工作正常")
      },
    })

    // 测试旧的方法
    recorder.setEnableVideo(false)
    console.log("✓ setEnableVideo 方法工作正常")

    const audioPermission = await recorder.checkRecordPermission()
    console.log("✓ checkRecordPermission 方法工作正常")

    // 测试 onUpload 属性
    recorder.onUpload = ({ type, questionId, url }) => {
      console.log("✓ onUpload 回调设置成功")
    }

    console.log("✓ 向后兼容性测试通过")
  } catch (error) {
    console.log("✗ 向后兼容性测试失败:", error.message)
  }

  return recorder
}

async function runAllTests() {
  console.log("开始 MediaRecorder 新架构测试...\n")

  try {
    await testBasicFunctionality()
    await testEventSystem()
    await testRecordingFlow()
    await testErrorHandling()
    await testBackwardCompatibility()

    console.log("\n=== 测试总结 ===")
    console.log("✓ 所有测试完成")
    console.log("✓ 新架构基本功能正常")
    console.log("✓ 事件系统工作正常")
    console.log("✓ 错误处理机制有效")
    console.log("✓ 向后兼容性良好")
  } catch (error) {
    console.log("\n✗ 测试过程中出现未捕获错误:", error)
  }
}

// 运行测试
if (require.main === module) {
  runAllTests()
}

module.exports = {
  testBasicFunctionality,
  testEventSystem,
  testRecordingFlow,
  testErrorHandling,
  testBackwardCompatibility,
  runAllTests,
}
